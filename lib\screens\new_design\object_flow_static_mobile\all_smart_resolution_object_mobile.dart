import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class AllSmartResolutionObjectMobile extends StatefulWidget {
  const AllSmartResolutionObjectMobile({super.key});

  @override
  State<AllSmartResolutionObjectMobile> createState() =>
      _AllSmartResolutionObjectMobileState();
}

class _AllSmartResolutionObjectMobileState
    extends State<AllSmartResolutionObjectMobile> {
  // JSON data for the validation rules
  final Map<String, dynamic> validationData = {
    "header": {
      "title": "All Smart Resolution",
      "bulkApplyButton": "Bulk Apply"
    },
    "validationRules": [
      {
        "id": 1,
        "title": "Email Validation Rules",
        "description":
            "Add IS_VALID_EMAIL and IS_UNIQUE operators for email field",
        "status": "SIMPLE",
        "actionType": "Business Rules",
        "hasCircleIcon": false
      },
      {
        "id": 2,
        "title": "Phone Format Validation",
        "description":
            "Apply MATCHES_PATTERN operator for international phone numbers",
        "status": "MODERATE",
        "actionType": "Business Rules",
        "hasCircleIcon": false
      },
      {
        "id": 3,
        "title": "Customer-Address Relationship",
        "description":
            "Configure one-to-many with CASCADE delete for address cleanup",
        "status": "MODERATE",
        "actionType": "Attribute",
        "hasCircleIcon": false
      },
      {
        "id": 4,
        "title": "Title of the Issue",
        "description": "Description of the Issue",
        "status": "MODERATE",
        "actionType": "Entity Relationship",
        "hasCircleIcon": false
      },
      {
        "id": 5,
        "title": "Address Auto-Complete",
        "description": "Description of the Issue",
        "status": "MODERATE",
        "actionType": "Entity Relationship",
        "hasCircleIcon": false
      }
    ]
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            Expanded(
              child: _buildValidationRulesList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.auto_fix_high,
            color: Colors.black87,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            validationData['header']['title'],
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyLarge(context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
          const Spacer(),
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                // Add bulk apply functionality here
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: Colors.black,
                    width: 1,
                  ),
                ),
                child: Text(
                  validationData['header']['bulkApplyButton'],
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyInter,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationRulesList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: ListView.builder(
        itemCount: validationData['validationRules'].length,
        itemBuilder: (context, index) {
          final rule = validationData['validationRules'][index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildValidationRuleItem(
              rule['title'],
              rule['description'],
              rule['actionType'],
              rule['status'],
            ),
          );
        },
      ),
    );
  }

  Widget _buildValidationRuleItem(
    String title,
    String description,
    String actionType,
    String status,
  ) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // Add validation rule item tap functionality here
          print('Tapped on: $title');
        },
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            border: Border.all(
              color: const Color(0xFFE5E5E5),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: IntrinsicHeight(
            child: Row(
              children: [
                // Left blue accent bar
                Container(
                  width: 6,
                  decoration: const BoxDecoration(
                    color: Color(0xFF007AFF),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      bottomLeft: Radius.circular(8),
                    ),
                  ),
                ),
                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                            fontFamily: FontManager.fontFamilyInter,
                          ),
                        ),
                        const SizedBox(height: 4),
                        // Description
                        Text(
                          description,
                          style: TextStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF666666),
                            fontFamily: FontManager.fontFamilyInter,
                          ),
                        ),
                        const SizedBox(height: 12),
                        // Action type and status row
                        Row(
                          children: [
                            Text(
                              actionType,
                              style: TextStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                color: const Color(0xFF666666),
                                fontFamily: FontManager.fontFamilyInter,
                              ),
                            ),
                            const Spacer(),
                            // Status badge
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getComplexityColor(status),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                status,
                                style: TextStyle(
                                  fontSize:
                                      ResponsiveFontSizes.labelSmall(context),
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black,
                                  fontFamily: FontManager.fontFamilyInter,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getComplexityColor(String complexity) {
    switch (complexity.toUpperCase()) {
      case 'SIMPLE':
        return const Color(0xFFE8F5E8);
      case 'MODERATE':
        return const Color(0xFFFFF4E6);
      case 'COMPLEX':
        return const Color(0xFFFFE6E6);
      default:
        return const Color(0xFFF5F5F5);
    }
  }
}
